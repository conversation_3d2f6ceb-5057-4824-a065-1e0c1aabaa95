#!/bin/bash

# 定义变量
USER_HOME="/home/<USER>"
CONFIG_DIR="$USER_HOME/.config/autostart"
WEBRTC_DIR="$USER_HOME/webrtc"
DESKTOP_FILE="$CONFIG_DIR/webrtc-auto.desktop"
SCRIPT_FILE="$WEBRTC_DIR/auto_request.sh"

# 创建必要的目录结构
mkdir -p $CONFIG_DIR
mkdir -p $WEBRTC_DIR

# 创建自启动配置文件内容
cat <<EOL > $DESKTOP_FILE
[Desktop Entry]
Type=Application
Name=WebRTC Auto Start
Comment=Auto start WebRTC script
Exec=$WEBRTC_DIR/auto_request.sh
Terminal=false
X-GNOME-Autostart-enabled=true
EOL

# 设置配置文件权限
chmod 644 $DESKTOP_FILE
chown orangepi:orangepi $DESKTOP_FILE

# 创建脚本文件内容
cat <<'EOL' > $SCRIPT_FILE
#!/bin/bash

# 设置环境变量
export DISPLAY=:0
export XAUTHORITY=/home/<USER>/.Xauthority
export HOME=/home/<USER>
export DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus

# 设置房间ID
ROOM_ID="123456"

# 等待系统完全启动
sleep 30

# 设置请求URL和成功后打开的页面URL
URL="http://*************:8080/webrtc/requestWebRTC?roomId=${ROOM_ID}"
SUCCESS_PAGE="https://ybda.top/simple-voice-call.html?roomId=${ROOM_ID}&autoJoin=true"

# 检查是否安装了curl
if ! command -v curl &> /dev/null; then
    # echo "请先安装curl: sudo apt-get install curl"
    exit 1
fi

# 设置浏览器命令
if command -v chromium-browser &> /dev/null; then
    BROWSER_CMD="chromium-browser"
elif command -v firefox &> /dev/null; then
    BROWSER_CMD="firefox"
else
    # echo "未找到浏览器，请安装chromium-browser或firefox"
    # echo "可以运行: sudo apt-get install chromium-browser"
    exit 1
fi

# 函数：检查浏览器进程
check_browser() {
    pgrep chromium > /dev/null
    return $?
}

# 函数：启动浏览器
start_browser() {
    # echo "正在启动浏览器..."
    $BROWSER_CMD --use-fake-ui-for-media-stream --enable-features=WebRTCPipeWireCapturer "$SUCCESS_PAGE" &
}

# 函数：关闭浏览器
close_browser() {
    # echo "正在关闭浏览器..."
    if [ "$BROWSER_CMD" = "chromium-browser" ]; then
        pkill chromium
    elif [ "$BROWSER_CMD" = "firefox" ]; then
        pkill firefox
    fi
}

# 设置退出时的清理操作
trap close_browser EXIT

# echo "开始监控..."
# echo "按 Ctrl+C 可以停止脚本"

# 主循环
while true; do
    # 发送HTTP请求
    response=$(curl -s $URL)
    code=$(echo $response | grep -o '"code":[0-9]*' | cut -d':' -f2)
    current_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 检查浏览器是否在运行
    if ! check_browser; then
        # echo "$current_time - 浏览器未运行"
        
        if [ "$code" = "200" ]; then
            # echo "检测到房间可用(200)，正在启动浏览器..."
            start_browser
        else
            # echo "等待房间可用... (当前状态: $code)"
			 :
        fi
    else
        # echo "$current_time - 浏览器运行中"
        
        if [ "$code" = "500" ]; then
            # echo "检测到房间不可用(500)，关闭浏览器..."
            close_browser
        fi
    fi
    
    # 每2秒检查一次
    sleep 2
done
EOL

# 设置脚本执行权限
chmod +x $SCRIPT_FILE

echo "配置完成！请确保所有路径和命令都符合您的需求。"