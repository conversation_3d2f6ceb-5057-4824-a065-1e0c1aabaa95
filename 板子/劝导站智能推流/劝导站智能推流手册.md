# 农村两轮车监控系统 - 智能推流解决方案

## 🎯 核心问题解决

**解决问题**：前端关闭视频后推流还在继续
**解决方案**：真正的按需推流 + 持续观看者监控 + 观看者离开后5秒内停止推流

## 🚀 快速部署（一步完成）

### 1. 一键部署并自动启动
```bash
sudo ./smart-stream-deploy.sh
```

部署完成后，推流服务自动启动，开机自启动已配置。

## 📁 文件说明

### 核心文件（仅2个）
- **smart-stream-deploy.sh** - 一键部署脚本，自动配置所有功能
- **README.md** - 本说明文档
- **manage-multi-camera.sh** - 监控脚本

### 自动生成的文件
- `/home/<USER>/srs/multi_camera_push.py` - 智能推流主程序
- `/home/<USER>/srs/camera_config.json` - 摄像头配置文件

## 📝 日志系统

### 文件日志方案
本系统使用传统的文件日志，日志存储在 `/home/<USER>/srs/logs/multi-camera-push.log`

### 查看日志命令
```bash
# 实时查看日志
tail -f /home/<USER>/srs/logs/multi-camera-push.log

# 查看最近50条日志
tail -n 50 /home/<USER>/srs/logs/multi-camera-push.log

# 查看包含特定关键词的日志
grep "ERROR" /home/<USER>/srs/logs/multi-camera-push.log

# 查看今天的日志
grep "$(date +%Y-%m-%d)" /home/<USER>/srs/logs/multi-camera-push.log
```

### 自动轮转策略
- **保留时间**：7天（自动轮转压缩）
- **轮转方式**：每日轮转，保留7个压缩文件
- **配置文件**：`/etc/logrotate.d/multi-camera-push`

### 手动清理命令
```bash
# 立即执行日志轮转
sudo logrotate -f /etc/logrotate.d/multi-camera-push

# 查看日志文件大小
ls -lh /home/<USER>/srs/logs/
```

## 🎮 系统管理

### 服务管理
```bash
# 查看服务状态
sudo systemctl status multi-camera-push

# 重启服务
sudo systemctl restart multi-camera-push

# 停止服务
sudo systemctl stop multi-camera-push

# 启动服务
sudo systemctl start multi-camera-push

# 禁用开机自启
sudo systemctl disable multi-camera-push
```

## 🔧 技术特点

### 智能推流机制
- **按需推流**：无观看者时不推流，节省带宽和系统资源
- **持续监控**：推流期间每5秒检测观看者状态
- **快速响应**：观看者离开后5秒内停止推流
- **智能重试**：观看者检测失败后固定5秒重试

### 日志特点
- **智能记录**：仅在状态变化时记录，避免重复日志
- **简洁易懂**：使用图标+文字格式（📡📊✅⚠️❌）
- **时间精确**：精确到秒的时间戳 [HH:MM:SS]
- **分级清晰**：INFO/SUCCESS/WARNING/ERROR 四级分类
- **文件存储**：所有日志存储在文件中，便于查看和管理

### 系统优化
- **多摄像头支持**：同时支持多个摄像头独立推流
- **自动重连**：网络异常或摄像头重启后自动恢复
- **开机自启**：系统重启后自动启动推流服务
- **资源优化**：空闲时几乎不占用系统资源

## 📊 推流地址格式

```
rtmp://服务器IP:端口/live/流名称
```

例如：
- device120: `rtmp://***************:1935/live/stream-device120`
- device130: `rtmp://***************:1935/live/stream-device130`

## 🛠️ 故障排查

### 常见问题

**1. 推流失败**
```bash
# 检查网络连接
ping ***************

# 检查后端API
curl http://***************:8081/api/stream/status

# 检查服务状态
sudo systemctl status multi-camera-push
```

**2. 日志查看**
```bash
# 查看错误日志
grep "ERROR" /home/<USER>/srs/logs/multi-camera-push.log

# 查看完整日志
cat /home/<USER>/srs/logs/multi-camera-push.log
```

**3. 重新部署**
```bash
# 停止服务
sudo systemctl stop multi-camera-push

# 重新运行部署脚本
sudo ./smart-stream-deploy.sh
```

### 快速检测
部署脚本会自动显示服务状态，也可以手动检查：
```bash
# 检查服务状态
sudo systemctl status multi-camera-push

# 查看实时日志
tail -f /home/<USER>/srs/logs/multi-camera-push.log

# 使用管理脚本（如果有）
./manage-multi-camera.sh
```

## 📋 部署环境要求

- **操作系统**：Ubuntu/Debian（香橙派支持）
- **Python版本**：Python 3.6+
- **必需软件**：FFmpeg, Python3, requests库
- **网络要求**：能访问摄像头IP和SRS服务器
- **权限要求**：需要sudo权限进行系统配置

## 🎯 核心优势

1. **真正解决问题**：彻底解决前端关闭后推流继续的问题
2. **部署极简**：一个命令完成所有配置，仅需2个文件
3. **运行稳定**：多线程设计，故障自动恢复
4. **日志统一**：使用文件日志，便于查看和管理
5. **自动轮转**：日志自动轮转压缩，不占用过多存储空间
6. **易于维护**：简洁的架构，便于管理和故障排查

 