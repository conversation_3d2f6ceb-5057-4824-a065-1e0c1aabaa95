#!/bin/bash

# 多摄像头管理脚本
# 农村两轮车监控系统 - 单设备多摄像头管理工具

set -e

COLOR_RED='\033[0;31m'
COLOR_GREEN='\033[0;32m'
COLOR_YELLOW='\033[1;33m'
COLOR_BLUE='\033[0;34m'
COLOR_PURPLE='\033[0;35m'
COLOR_CYAN='\033[0;36m'
COLOR_NC='\033[0m' # No Color
SERVICE_NAME="multi-camera-push"
BASE_DIR="/home/<USER>/srs"
CONFIG_FILE="$BASE_DIR/camera_config.json"
LOG_FILE="$BASE_DIR/logs/multi-camera-push.log"

echo -e "${COLOR_CYAN}🍊 香橙派多摄像头管理工具${COLOR_NC}"
echo "=================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${COLOR_RED}❌ 请使用sudo运行此脚本: sudo ./manage-multi-camera.sh${COLOR_NC}"
    exit 1
fi

# 环境检查
echo "🔍 检查系统环境..."

# 检查服务是否存在
if ! systemctl list-units --all | grep -q "$SERVICE_NAME.service"; then
    echo -e "${COLOR_RED}❌ 未找到多摄像头推流服务${COLOR_NC}"
    echo -e "${COLOR_YELLOW}💡 请先运行部署脚本: sudo ./single-device-multi-stream-deploy.sh${COLOR_NC}"
    exit 1
fi

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${COLOR_YELLOW}⚠️  配置文件不存在: $CONFIG_FILE${COLOR_NC}"
fi

# 检查日志目录
if [ ! -d "$(dirname "$LOG_FILE")" ]; then
    echo -e "${COLOR_YELLOW}⚠️  日志目录不存在，将自动创建${COLOR_NC}"
    mkdir -p "$(dirname "$LOG_FILE")"
    chown orangepi:orangepi "$(dirname "$LOG_FILE")"
fi

echo -e "${COLOR_GREEN}✅ 环境检查完成${COLOR_NC}"

# 函数：显示菜单
show_menu() {
    echo ""
    echo -e "${COLOR_BLUE}📋 多摄像头管理菜单:${COLOR_NC}"
    echo "1) 📊 查看服务状态"
    echo "2) 🚀 启动推流服务"
    echo "3) ⏹️  停止推流服务"
    echo "4) 🔄 重启推流服务"
    echo "5) 📝 查看实时日志"
    echo "6) 🔧 查看配置信息"
    echo "7) 📈 系统资源监控"
    echo "8) 🎮 查看推流地址"
    echo "9) 🧹 清理日志文件"
    echo "0) 🚪 退出"
    echo ""
}

# 函数：检查服务是否存在
check_service_exists() {
    if ! systemctl list-units --all | grep -q "$SERVICE_NAME.service"; then
        echo -e "${COLOR_RED}❌ 服务 $SERVICE_NAME 不存在，请先运行部署脚本${COLOR_NC}"
        return 1
    fi
    return 0
}

# 函数：查看服务状态
show_service_status() {
    echo -e "${COLOR_GREEN}📊 多摄像头推流服务状态${COLOR_NC}"
    echo "=============================="
    
    if ! check_service_exists; then
        return
    fi
    
    # 检查服务状态
    status=$(systemctl is-active $SERVICE_NAME 2>/dev/null || echo "inactive")
    enabled=$(systemctl is-enabled $SERVICE_NAME 2>/dev/null || echo "disabled")
    
    if [ "$status" = "active" ]; then
        echo -e "${COLOR_GREEN}✅ 服务状态: 运行中${COLOR_NC}"
    else
        echo -e "${COLOR_RED}❌ 服务状态: 已停止${COLOR_NC}"
    fi
    
    echo "🔧 开机启动: $enabled"
    
    echo ""
    echo -e "${COLOR_BLUE}详细状态信息:${COLOR_NC}"
    systemctl status $SERVICE_NAME --no-pager -l || true
    
    # 显示摄像头配置
    if [ -f "$CONFIG_FILE" ]; then
        echo ""
        echo -e "${COLOR_BLUE}📹 摄像头配置:${COLOR_NC}"
        if command -v jq >/dev/null 2>&1; then
            jq -r '.cameras[] | "  📷 \(.name): \(.ip) -> \(.stream_name)"' "$CONFIG_FILE"
        else
            echo "  配置文件: $CONFIG_FILE"
        fi
    fi
}

# 函数：启动服务
start_service() {
    echo -e "${COLOR_GREEN}🚀 启动多摄像头推流服务${COLOR_NC}"
    
    if ! check_service_exists; then
        return
    fi
    
    if systemctl start $SERVICE_NAME; then
        echo -e "${COLOR_GREEN}✅ 服务启动成功${COLOR_NC}"
        sleep 3
        show_service_status
    else
        echo -e "${COLOR_RED}❌ 服务启动失败${COLOR_NC}"
        echo "查看错误日志:"
        if [ -f "$LOG_FILE" ]; then
            tail -n 10 "$LOG_FILE"
        else
            echo "日志文件不存在: $LOG_FILE"
        fi
    fi
}

# 函数：停止服务
stop_service() {
    echo -e "${COLOR_YELLOW}⏹️  停止多摄像头推流服务${COLOR_NC}"
    
    if ! check_service_exists; then
        return
    fi
    
    if systemctl stop $SERVICE_NAME; then
        echo -e "${COLOR_GREEN}✅ 服务停止成功${COLOR_NC}"
        sleep 2
        show_service_status
    else
        echo -e "${COLOR_RED}❌ 服务停止失败${COLOR_NC}"
    fi
}

# 函数：重启服务
restart_service() {
    echo -e "${COLOR_BLUE}🔄 重启多摄像头推流服务${COLOR_NC}"
    
    if ! check_service_exists; then
        return
    fi
    
    if systemctl restart $SERVICE_NAME; then
        echo -e "${COLOR_GREEN}✅ 服务重启成功${COLOR_NC}"
        sleep 5
        show_service_status
    else
        echo -e "${COLOR_RED}❌ 服务重启失败${COLOR_NC}"
        echo "查看错误日志:"
        if [ -f "$LOG_FILE" ]; then
            tail -n 10 "$LOG_FILE"
        else
            echo "日志文件不存在: $LOG_FILE"
        fi
    fi
}

# 函数：查看实时日志
show_realtime_logs() {
    echo -e "${COLOR_PURPLE}📝 实时日志监控${COLOR_NC}"
    echo "=============================="
    echo "按 Ctrl+C 退出日志监控"
    echo ""
    
    if ! check_service_exists; then
        return
    fi
    
    # 显示最近的一些日志
    echo "最近20条日志:"
    if [ -f "$LOG_FILE" ]; then
        tail -n 20 "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
    
    echo ""
    echo "实时日志 (Ctrl+C 退出):"
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
        echo "服务可能还未启动或配置有误"
    fi
}

# 函数：查看配置信息
show_config_info() {
    echo -e "${COLOR_CYAN}🔧 配置信息${COLOR_NC}"
    echo "=============================="
    
    if [ -f "$CONFIG_FILE" ]; then
        echo "配置文件: $CONFIG_FILE"
        echo ""
        
        if command -v jq >/dev/null 2>&1; then
            echo -e "${COLOR_BLUE}SRS服务器:${COLOR_NC}"
            jq -r '.srs_server' "$CONFIG_FILE"
            echo ""
            
            echo -e "${COLOR_BLUE}摄像头列表:${COLOR_NC}"
            jq -r '.cameras[] | "📷 \(.name):\n   IP: \(.ip)\n   流名称: \(.stream_name)\n   RTSP: \(.rtsp_url)\n"' "$CONFIG_FILE"
        else
            echo "配置文件内容:"
            cat "$CONFIG_FILE"
            echo ""
            echo "💡 提示: 安装jq可获得更好的显示效果: sudo apt install jq"
        fi
    else
        echo -e "${COLOR_RED}❌ 配置文件不存在: $CONFIG_FILE${COLOR_NC}"
    fi
    
    echo ""
    echo -e "${COLOR_BLUE}服务文件:${COLOR_NC}"
    if systemctl cat $SERVICE_NAME >/dev/null 2>&1; then
        echo "服务配置:"
        systemctl cat $SERVICE_NAME | head -20
    else
        echo "服务文件不存在"
    fi
}

# 函数：系统资源监控
show_system_monitor() {
    echo -e "${COLOR_GREEN}📈 系统资源监控${COLOR_NC}"
    echo "=============================="
    
    echo -e "${COLOR_BLUE}💾 内存使用:${COLOR_NC}"
    free -h
    echo ""
    
    echo -e "${COLOR_BLUE}💽 磁盘使用:${COLOR_NC}"
    df -h | grep -E "(文件系统|Filesystem|/dev/)"
    echo ""
    
    echo -e "${COLOR_BLUE}🔄 CPU负载:${COLOR_NC}"
    uptime
    echo ""
    
    echo -e "${COLOR_BLUE}🌐 网络连接:${COLOR_NC}"
    netstat -tuln | grep -E "(1935|8080|80|22)" || echo "未找到相关端口"
    echo ""
    
    echo -e "${COLOR_BLUE}🎮 FFmpeg进程:${COLOR_NC}"
    ps aux | grep -E "(ffmpeg|multi_camera_push)" | grep -v grep || echo "无相关进程运行"
    echo ""
    
    echo -e "${COLOR_BLUE}📊 系统进程 (按CPU排序):${COLOR_NC}"
    ps aux --sort=-%cpu | head -10
    echo ""
    
    echo -e "${COLOR_BLUE}🔥 系统温度 (如可用):${COLOR_NC}"
    if [ -f "/sys/class/thermal/thermal_zone0/temp" ]; then
        temp=$(cat /sys/class/thermal/thermal_zone0/temp)
        temp_c=$((temp / 1000))
        echo "CPU温度: ${temp_c}°C"
    else
        echo "温度信息不可用"
    fi
}

# 函数：查看推流地址
show_stream_urls() {
    echo -e "${COLOR_CYAN}🎮 推流地址信息${COLOR_NC}"
    echo "=============================="
    
    if [ -f "$CONFIG_FILE" ]; then
        if command -v jq >/dev/null 2>&1; then
            srs_server=$(jq -r '.srs_server' "$CONFIG_FILE")
            echo -e "${COLOR_BLUE}📡 SRS服务器: $srs_server${COLOR_NC}"
            echo ""
            echo -e "${COLOR_BLUE}📺 推流地址:${COLOR_NC}"
            jq -r '.cameras[] | "🎬 \(.name): rtmp://\(env.srs_server // "SRS_SERVER")/live/\(.stream_name)"' --arg srs_server "$srs_server" "$CONFIG_FILE"
            echo ""
            echo -e "${COLOR_BLUE}🌐 播放地址 (WebRTC):${COLOR_NC}"
            srs_host=$(echo $srs_server | cut -d: -f1)
            jq -r '.cameras[] | "📱 \(.name): http://\(env.srs_host):8080/players/rtc_player.html?stream=\(.stream_name)"' --arg srs_host "$srs_host" "$CONFIG_FILE"
        else
            echo "配置文件存在，但需要jq来解析"
            echo "安装jq: sudo apt install jq"
        fi
    else
        echo -e "${COLOR_RED}❌ 配置文件不存在${COLOR_NC}"
    fi
}

# 函数：清理日志文件
clean_logs() {
    echo -e "${COLOR_YELLOW}🧹 日志清理${COLOR_NC}"
    echo "=============================="
    
    if [ -f "$LOG_FILE" ]; then
        log_size=$(du -h "$LOG_FILE" | cut -f1)
        echo "当前日志文件大小: $log_size"
        echo "日志文件: $LOG_FILE"
        echo ""
        
        read -p "确认清理日志文件? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            > "$LOG_FILE"  # 清空文件内容
            echo -e "${COLOR_GREEN}✅ 应用日志清理完成${COLOR_NC}"
        else
            echo "取消清理操作"
        fi
    else
        echo "应用日志文件不存在: $LOG_FILE"
    fi
    
    echo ""
    read -p "是否执行日志轮转清理? (y/N): " clean_rotation
    if [[ $clean_rotation =~ ^[Yy]$ ]]; then
        echo "执行日志轮转清理..."
        if [ -f "/etc/logrotate.d/multi-camera-push" ]; then
            logrotate -f /etc/logrotate.d/multi-camera-push
            echo -e "${COLOR_GREEN}✅ 日志轮转清理完成${COLOR_NC}"
        else
            echo "日志轮转配置文件不存在"
        fi
    fi
}

# 主程序循环
main() {
    while true; do
        show_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1)
                show_service_status
                ;;
            2)
                start_service
                ;;
            3)
                stop_service
                ;;
            4)
                restart_service
                ;;
            5)
                show_realtime_logs
                ;;
            6)
                show_config_info
                ;;
            7)
                show_system_monitor
                ;;
            8)
                show_stream_urls
                ;;
            9)
                clean_logs
                ;;
            0)
                echo -e "${COLOR_GREEN}👋 再见！${COLOR_NC}"
                exit 0
                ;;
            *)
                echo -e "${COLOR_RED}❌ 无效选择，请输入 0-9${COLOR_NC}"
                ;;
        esac
        
        echo ""
        read -p "按回车继续..."
    done
}

# 运行主程序
main 