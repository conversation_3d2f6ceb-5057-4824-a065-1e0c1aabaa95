package com.demo.service.impl;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.entity.VillageInformation;
import com.demo.mapper.VillageInformationMapper;
import com.demo.service.IVillageInformationService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class VillageInformationServiceImpl extends ServiceImpl<VillageInformationMapper, VillageInformation> implements IVillageInformationService {
    @Override
    public SaResult getBasicDataByMap(String city, String county, String township, String hamlet, String site) {
        // 构建查询条件
        QueryWrapper<VillageInformation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(org.apache.commons.lang.StringUtils.isNotBlank(city), "city", city);
        queryWrapper.eq(org.apache.commons.lang.StringUtils.isNotBlank(county), "county", county);
        queryWrapper.eq(org.apache.commons.lang.StringUtils.isNotBlank(township), "township", township);
        queryWrapper.eq(org.apache.commons.lang.StringUtils.isNotBlank(hamlet), "hamlet", hamlet);
        // 移除点位级别的查询，因为基础数据只到乡镇级
        // 查询符合条件的数据
        List<VillageInformation> villageList = baseMapper.selectList(queryWrapper);
        if (villageList.isEmpty()) {
            return SaResult.error("未找到相关数据");
        }
        // 根据查询层级决定如何汇总数据
        if (hamlet != null && !hamlet.isEmpty()) {
            // 如果查询到社区级别，返回社区详细信息
            return handleHamletData(villageList);
        } else if (township != null && !township.isEmpty()) {
            // 如果查询到街道级别，按社区汇总
            return handleTownshipData(villageList);
        } else if (county != null && !county.isEmpty()) {
            // 如果查询到区县级别，按街道汇总
            return handleCountyData(villageList);
        } else {
            // 如果只有城市或没有筛选条件，按区县汇总
            return handleCityData(villageList);
        }
    }
    
    // 处理社区级别数据
    private SaResult handleHamletData(List<VillageInformation> villageList) {
        VillageInformation village = villageList.get(0);
        
        // 构建社区详细信息
        Map<String, Object> hamletData = new HashMap<>();
        hamletData.put("name", village.getHamlet());
        hamletData.put("area", String.format("%.2f", village.getAreaOfJurisdiction()));
        hamletData.put("population", String.format("%d", village.getRegisteredPopulation()));
        // 可以添加更多社区详细信息
        
        return SaResult.data(hamletData);
    }
    
    // 处理街道级别数据，按社区汇总
    private SaResult handleTownshipData(List<VillageInformation> villageList) {
        // 计算街道总面积和人口
        double totalArea = villageList.stream()
                .mapToDouble(VillageInformation::getAreaOfJurisdiction)
                .sum();
        int totalPopulation = villageList.stream()
                .mapToInt(VillageInformation::getRegisteredPopulation)
                .sum();
        
        // 构建社区列表
        List<Map<String, Object>> communities = villageList.stream()
                .map(v -> {
                    Map<String, Object> community = new HashMap<>();
                    community.put("name", v.getHamlet());
                    community.put("area", String.format("%.2f", v.getAreaOfJurisdiction()));
                    community.put("population", String.format("%d", v.getRegisteredPopulation()));
                    return community;
                })
                .collect(Collectors.toList());
        
        // 构建街道汇总数据
        Map<String, Object> townshipData = new HashMap<>();
        townshipData.put("name", villageList.get(0).getTownship());
        townshipData.put("area", String.format("%.2f", totalArea));
        townshipData.put("population", String.format("%d", totalPopulation));
        townshipData.put("point", communities);
        
        return SaResult.data(townshipData);
    }
    
    // 处理区县级别数据，按街道汇总
    private SaResult handleCountyData(List<VillageInformation> villageList) {
        // 按街道分组
        Map<String, List<VillageInformation>> townshipGroups = villageList.stream()
                .collect(Collectors.groupingBy(VillageInformation::getTownship));
        
        // 计算区县总面积和人口
        double totalCountyArea = villageList.stream()
                .mapToDouble(VillageInformation::getAreaOfJurisdiction)
                .sum();
        int totalCountyPopulation = villageList.stream()
                .mapToInt(VillageInformation::getRegisteredPopulation)
                .sum();
        
        // 构建街道列表
        List<Map<String, Object>> townships = new ArrayList<>();
        for (Map.Entry<String, List<VillageInformation>> entry : townshipGroups.entrySet()) {
            String townshipName = entry.getKey();
            List<VillageInformation> townVillages = entry.getValue();
            
            // 计算街道总面积和人口
            double townshipArea = townVillages.stream()
                    .mapToDouble(VillageInformation::getAreaOfJurisdiction)
                    .sum();
            int townshipPopulation = townVillages.stream()
                    .mapToInt(VillageInformation::getRegisteredPopulation)
                    .sum();
            
            Map<String, Object> township = new HashMap<>();
            township.put("name", townshipName);
            township.put("area", String.format("%.2f", townshipArea));
            township.put("population", String.format("%d", townshipPopulation));
            
            townships.add(township);
        }
        
        // 构建区县汇总数据
        Map<String, Object> countyData = new HashMap<>();
        countyData.put("name", villageList.get(0).getCounty());
        countyData.put("area", String.format("%.2f", totalCountyArea));
        countyData.put("population", String.format("%d", totalCountyPopulation));
        countyData.put("point", townships);
        
        return SaResult.data(countyData);
    }
    
    // 处理城市级别数据，按区县汇总
    private SaResult handleCityData(List<VillageInformation> villageList) {
        // 按区县分组
        Map<String, List<VillageInformation>> countyGroups = villageList.stream()
                .collect(Collectors.groupingBy(VillageInformation::getCounty));
        
        // 计算城市总面积和人口
        double totalCityArea = villageList.stream()
                .mapToDouble(VillageInformation::getAreaOfJurisdiction)
                .sum();
        int totalCityPopulation = villageList.stream()
                .mapToInt(VillageInformation::getRegisteredPopulation)
                .sum();
        
        // 构建区县列表
        List<Map<String, Object>> counties = new ArrayList<>();
        for (Map.Entry<String, List<VillageInformation>> entry : countyGroups.entrySet()) {
            String countyName = entry.getKey();
            List<VillageInformation> countyVillages = entry.getValue();
            
            // 计算区县总面积和人口
            double countyArea = countyVillages.stream()
                    .mapToDouble(VillageInformation::getAreaOfJurisdiction)
                    .sum();
            int countyPopulation = countyVillages.stream()
                    .mapToInt(VillageInformation::getRegisteredPopulation)
                    .sum();
            
            Map<String, Object> county = new HashMap<>();
            county.put("name", countyName);
            county.put("area", String.format("%.2f", countyArea));
            county.put("population", String.format("%d", countyPopulation));
            
            counties.add(county);
        }
        
        // 构建城市汇总数据
        Map<String, Object> cityData = new HashMap<>();
        cityData.put("name", villageList.get(0).getCity());
        cityData.put("area", String.format("%.2f", totalCityArea));
        cityData.put("population", String.format("%d", totalCityPopulation));
        cityData.put("point", counties);
        
        return SaResult.data(cityData);
    }
}
