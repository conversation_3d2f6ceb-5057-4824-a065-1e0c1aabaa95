package com.demo.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.entity.VideoRecording;
import com.demo.mapper.VideoRecordingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;

/**
 * 录制文件扫描服务
 * 启动时扫描录制目录，将未入库的文件添加到数据库
 */
@Slf4j
@Service
public class RecordingScanService implements ApplicationRunner {

    private static final String RECORDINGS_BASE_PATH = "/home/<USER>/live/";
    @Autowired
    private VideoRecordingMapper videoRecordingMapper;

    @Override
    public void run(ApplicationArguments args)  {
        log.info("应用启动时开始扫描录制文件目录...");
        scanRecordingDirectory();
    }

    /**
     * 扫描录制目录并同步到数据库
     */
    public void scanRecordingDirectory() {
        try {
            File baseDir = new File(RECORDINGS_BASE_PATH);
            if (!baseDir.exists()) {
                log.warn("录制目录不存在: {}", RECORDINGS_BASE_PATH);
//                return SaResult.error("录制目录不存在");
            }
            int scannedCount = 0;
            int addedCount = 0;
            // 遍历所有流目录
            File[] streamDirs = baseDir.listFiles(File::isDirectory);
            if (streamDirs != null) {
                for (File streamDir : streamDirs) {
                    String streamName = streamDir.getName();
                    log.info("扫描流目录: {}", streamName);
                    int[] counts = scanStreamDirectory(streamDir, streamName);
                    scannedCount += counts[0];
                    addedCount += counts[1];
                }
            }
            log.info("录制文件扫描完成: 扫描{}个文件，新增{}个记录", scannedCount, addedCount);
//            return SaResult.ok("扫描完成").setData("scanned=" + scannedCount + ", added=" + addedCount);
        } catch (Exception e) {
            log.error("扫描录制文件异常: {}", e.getMessage(), e);
//            return SaResult.error("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 扫描单个流的录制目录
     */
    private int[] scanStreamDirectory(File streamDir, String streamName) {
        int scannedCount = 0;
        int addedCount = 0;

        try {
            // 递归扫描所有.mp4文件
            int[] counts = scanDirectoryRecursive(streamDir, streamName);
            scannedCount = counts[0];
            addedCount = counts[1];
        } catch (Exception e) {
            log.error("扫描流目录异常: {}, error: {}", streamName, e.getMessage());
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 递归扫描目录
     */
    private int[] scanDirectoryRecursive(File dir, String streamName) {
        int scannedCount = 0;
        int addedCount = 0;

        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    int[] subCounts = scanDirectoryRecursive(file, streamName);
                    scannedCount += subCounts[0];
                    addedCount += subCounts[1];
                } else if (file.getName().endsWith(".mp4")) {
                    scannedCount++;
                    if (addRecordingToDatabase(file, streamName)) {
                        addedCount++;
                    }
                }
            }
        }

        return new int[]{scannedCount, addedCount};
    }

    /**
     * 将录制文件添加到数据库
     */
    private boolean addRecordingToDatabase(File file, String streamName) {
        try {
            String absolutePath = file.getAbsolutePath();

            // 检查是否已存在
            LambdaQueryWrapper<VideoRecording> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VideoRecording::getFilePath, absolutePath);
            VideoRecording existing = videoRecordingMapper.selectOne(queryWrapper);

            if (existing != null) {
                // 已存在，跳过
                return false;
            }
            // 解析录制开始时间
            LocalDateTime startTime = parseRecordingStartTime(absolutePath, file);
            // 录制结束时间使用文件修改时间
            LocalDateTime endTime = LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(file.lastModified()),
                    java.time.ZoneId.systemDefault()
            );
            // 创建录制记录
            VideoRecording recording = new VideoRecording();
            recording.setStreamName(streamName);
            recording.setFilePath(absolutePath);
            recording.setFileName(file.getName());
            recording.setFileSize(file.length());
            recording.setStartTime(startTime);
            recording.setEndTime(endTime);
            recording.setCreatedTime(LocalDateTime.now());
            recording.setStatus(VideoRecording.STATUS_COMPLETED);
            // 计算录制时长
            long durationSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            recording.setDuration((int) durationSeconds);

            // 保存到数据库
            videoRecordingMapper.insert(recording);

            log.debug("添加录制文件到数据库: {}", file.getName());
            return true;
        } catch (Exception e) {
            log.error("添加录制文件到数据库失败: {}, error: {}", file.getName(), e.getMessage());
            return false;
        }
    }

    /**
     * 解析录制开始时间
     */
    private LocalDateTime parseRecordingStartTime(String filePath, File file) {
        try {
            // 尝试从文件名解析
            String fileName = file.getName();
            if (fileName.matches("[0-9]{2}\\.[0-9]{2}\\.[0-9]{2}\\.[0-9]{3}\\.mp4")) {
                String[] parts = fileName.replace(".mp4", "").split("\\.");
                String[] pathParts = filePath.split("/");

                if (parts.length == 4 && pathParts.length >= 4) {
                    int year = Integer.parseInt(pathParts[pathParts.length - 4]);
                    int month = Integer.parseInt(pathParts[pathParts.length - 3]);
                    int day = Integer.parseInt(pathParts[pathParts.length - 2]);
                    int hour = Integer.parseInt(parts[0]);
                    int minute = Integer.parseInt(parts[1]);
                    int second = Integer.parseInt(parts[2]);
                    int millisecond = Integer.parseInt(parts[3]);

                    return LocalDateTime.of(year, month, day, hour, minute, second, millisecond * 1000000);
                }
            }
            // 备选方案：使用文件修改时间减去30分钟
            LocalDateTime fileTime = LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(file.lastModified()),
                    java.time.ZoneId.systemDefault()
            );
            return fileTime.minusMinutes(30);

        } catch (Exception e) {
            log.warn("解析录制开始时间失败: {}, error: {}", filePath, e.getMessage());
            // 使用文件修改时间减去30分钟作为默认值
            LocalDateTime fileTime = LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(file.lastModified()),
                    java.time.ZoneId.systemDefault()
            );
            return fileTime.minusMinutes(30);
        }
    }
}
