package com.demo.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.demo.entity.Device;
import com.demo.entity.DeviceStatusHistory;
import com.demo.entity.Relatedconfigurations;
import com.demo.mapper.DeviceMapper;
import com.demo.mapper.DeviceStatusHistoryMapper;
import com.demo.mapper.RelatedconfigurationsMapper;
import com.demo.service.DeviceService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class RelatedConfigurations {
    @Autowired
    RelatedconfigurationsMapper relatedconfigurationsMapper;
    public static Relatedconfigurations relatedconfigurations;
    @Autowired
    DeviceStatusHistoryMapper deviceStatusHistoryMapper;
    @Autowired
    DeviceMapper deviceMapper;
    @Autowired
    DeviceService deviceService;
    @PostConstruct
    public void init() {
        //系统启动中。。。加载全局变量
        //加载路口信息
        log.info("全局变量开始加载");
        QueryWrapper<Relatedconfigurations> queryWrapper = new QueryWrapper<>();
        relatedconfigurations = relatedconfigurationsMapper.selectOne(queryWrapper);
        log.info("全局变量加载完毕");
        List<Device> allDevices = deviceMapper.selectList(null);
        Date now = new Date();
        Integer equipmentOfflineTime = relatedconfigurations.getEquipmentOfflineTime();
        long offlineTimeoutMs = equipmentOfflineTime * 60 * 1000L;
        for (Device device : allDevices) {
            DeviceStatusHistory lastHistory = deviceStatusHistoryMapper.selectOne(
                    new QueryWrapper<DeviceStatusHistory>()
                            .eq("equipment_number", device.getEquipmentNumber())
                            .eq("ip", device.getIp())
                            .orderByDesc("start_time")
                            .last("LIMIT 1")
            );
            if (lastHistory == null) {
                // 没有历史，插入初始化状态
                deviceService.recordDeviceStatusChange(device, device.getState(), "系统初始化状态");
            } else if (lastHistory.getEndTime() == null) {
                boolean needEnd = false;
                // 1. 状态不一致
                if (device.getState() != lastHistory.getState()) {
                    needEnd = true;
                }
                // 2. 在线超时未上报（仅当历史为在线且设备实际为在线时才判断超时）
                if (lastHistory.getState() == 0 && device.getState() == 0) {
                    if (device.getLastOnlineTime() == null || (now.getTime() - device.getLastOnlineTime().getTime()) > offlineTimeoutMs) {
                        needEnd = true; // 设备超时未上报，判定为离线
                    }
                }
                if (needEnd) {
                    lastHistory.setEndTime(now);
                    lastHistory.setDuration((now.getTime() - lastHistory.getStartTime().getTime()) / 1000);
                    deviceStatusHistoryMapper.updateById(lastHistory);
                    // 插入新状态
                    deviceService.recordDeviceStatusChange(device, device.getState(), "系统启动时状态切换");
                }
                // 如果needEnd为false，什么都不做，延续历史
            }
        }
    }
    
}
