package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@TableName("village_information")
public class VillageInformation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**ID*/
    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;
    /**市*/
    private String city;
    /**区县*/
    private String county;
    /**镇*/
    private String township;
    /**乡*/
    private String hamlet;
    /**点位*/
    private String site;
    /**辖区面积*/
    private Double areaOfJurisdiction;
    /**户籍人口*/
    private Integer registeredPopulation;
    /**类别*/
    private String category;

    @Data
    // 定义目标数据结构
    public static class ResultData {
        private List<County> continus;
    }
    @Data
    public static class County {
        private String name;
        private String area;
        private String population;
        private List<Town> towns;
    }
    @Data
    public static class Town {
        private String name;
        private String area;
        private String population;
        private List<Community> communities;
    }
    @Data
    public static class Community {
        private String name;
        private String area;
        private String population;

        public Community(String name, String area, String population) {
            this.name = name;
            this.area = area;
            this.population = population;
        }
    }

}
