package com.demo.entity.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户班次组信息的值对象（Value Object）
 * 该类用于封装从数据库查询得到的用户及其班次组的相关信息
 * <AUTHOR>
 */
@Data
public class AllThePersuadersVO {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户所属部门名称
     */
    private String deptName;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户所在区县
     */
    private String county;

    /**
     * 用户所在乡镇
     */
    private String township;

    /**
     * 用户所在村
     */
    private String hamlet;

    /**
     * 用户所在站点
     */
    private String site;

    /**
     * 用户状态
     */
    private Integer state;

    /**
     * 班次组ID
     */
    private Long groupId;

    /**
     * 班次组名称
     */
    private String groupName;

    /**
     * 班次开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 班次结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 班次所在星期
     */
    private String workPattern;
}
