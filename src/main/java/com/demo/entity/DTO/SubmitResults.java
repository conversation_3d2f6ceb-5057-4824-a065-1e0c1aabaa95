package com.demo.entity.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;


/**
 * 提交劝导结果
 *
 * <AUTHOR>
 */
@Data
public class SubmitResults {
    /**
     * 违法记录id
     */
    private String illegalRecordsUuid;

    /**
     * 处理方式(打电话还是上门)
     */
    private String disposalMethod;

    /**
     * 实际处理人员ID
     */
    private Integer actualProcessing;

    /**
     * 实际处理人员姓名
     */
    private String actualProcessingName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 违法人员姓名
     */
    private String illegalPersonnelName;

    /**
     * 违法人员身份证
     */
    private String illegalPersonnelIdCard;

    /**
     * 违法人员电话
     */
    private String illegalPersonnelPhone;

    /**
     * 违法人员性别
     */
    private String illegalPersonnelGender;
}
