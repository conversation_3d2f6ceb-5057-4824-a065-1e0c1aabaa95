package com.demo.entity.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class ExamineFaceDTO {
    /**
     * id
     */
    private Integer id;

    /**
     * 是否通过审核
     */
    private Boolean isExamine;

    /**
     * 对应人员
     */
    private Integer userId;

    /**
     * 对应人员姓名
     */
    private String userName;

    /**
     * 审核人姓名
     */
    private String reviewer;

    /**
     * 审核人ID
     */
    private Integer reviewerId;
}
