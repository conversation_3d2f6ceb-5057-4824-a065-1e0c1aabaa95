package com.demo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.demo.handler.IntegerArrayJSONTypeHandler;
import com.demo.handler.StringArrayJSONTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 设备表
 */
@Data
@TableName(value = "`device`" ,autoResultMap = true)
public class Device {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备名字
     */
    @TableField(value = "`device_name`")
    private String deviceName;

    /**
     * 设备编号
     */
    @TableField(value = "`equipment_number`")
    private String equipmentNumber;

    /**
     * 设备类型
     */
    @TableField(value = "`device_type`")
    private String deviceType;

    /**
     * 设备IP
     */
    @TableField(value = "`ip`")
    private String ip;

    /**
     * 流密钥（视频）
     */
    @TableField(value = "`stream_key`")
    private String streamKey;

    /**
     * 市
     */
    @TableField(value = "`city`")
    private String city;

    /**
     * 所在县（区）
     */
    @TableField(value = "`county`")
    private String county;

    /**
     * 所在乡（镇）
     */
    @TableField(value = "`township`")
    private String township;

    /**
     * 所在村
     */
    @TableField(value = "`hamlet`")
    private String hamlet;

    /**
     * 所在点位
     */
    @TableField(value = "`site`")
    private String site;

    /**
     * 维护人员电话
     */
    @TableField(value = "`maintainer_phone`")
    private String maintainerPhone;

    /**
     * 成功率
     */
    @TableField(value = "`scale`")
    private Double scale;

    /**
     * 状态（是否正常）0正常1不正常
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 经度
     */
    @TableField(value = "`longitude`")
    private String longitude;

    /**
     * 纬度
     */
    @TableField(value = "`latitude`")
    private String latitude;

    /**
     * 最后在线时间
     */
    @TableField(value = "`last_online_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastOnlineTime;

    /**
     * 修改时间
     */
    @TableField(value = "`update_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否为开发模式
     */
    @TableField(value = "`dev_mode`")
    private Boolean devMode;

    /**
     * 设备地址信息
     */
    @TableField(value = "`address_text`")
    private String addressText;

    /**
     * 车辆来向方向的摄像头RTSP地址
     */
    @TableField(value = "`camera_url1`")
    private String cameraUrl1;

    /**
     * 车辆去向方向的摄像头RTSP地址
     */
    @TableField(value = "`camera_url2`")
    private String cameraUrl2;

    /**
     * 来向区域画面裁剪
     */
    @TableField(value = "`clip_rect1`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] clipRect1;

    /**
     * 去向区域画面裁剪
     */
    @TableField(value = "`clip_rect2`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] clipRect2;

    /**
     * 来向区域碰线坐标
     */
    @TableField(value = "`line1`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] line1;

    /**
     * 去向区域碰线坐标
     */
    @TableField(value = "`line2`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] line2;

    /**
     * 没有带头盔的识别阈值
     */
    @TableField(value = "`nohelmet_threshold`")
    private Double nohelmetThreshold;

    /**
     * 雨棚识别的阈值
     */
    @TableField(value = "`weather_shield_threshold`")
    private Double weatherShieldThreshold;

    /**
     * 劝导行为识别有效期（秒）
     */
    @TableField(value = "`persuad_time_duration`")
    private Integer persuadTimeDuration;

    /**
     * 劝导员人脸识别间隔（秒）
     */
    @TableField(value = "`advicer_face_recog_duration`")
    private Integer advicerFaceRecogDuration;

    /**
     * 劝导员人脸识别阈值
     */
    @TableField(value = "`face_recog_distance_threshold`")
    private Double faceRecogDistanceThreshold;

    /**
     * 前端加载当前配置信息间隔（秒）
     */
    @TableField(value = "`load_param_interval`")
    private Integer loadParamInterval;

    /**
     * 前端加载人脸数据库间隔（秒）
     */
    @TableField(value = "`load_face_database_interval`")
    private Integer loadFaceDatabaseInterval;

    /**
     * 跟踪车辆ID的最长时间（秒）
     */
    @TableField(value = "`bike_trackid_life`")
    private Integer bikeTrackidLife;

    /**
     * 是否在设备上保存违法车辆视频
     */
    @TableField(value = "`save_violate_video`")
    private Boolean saveViolateVideo;

    /**
     * 记录违法视频前后的时长（秒）
     */
    @TableField(value = "`start_end_time`")
    private Integer startEndTime;

    /**
     * 违法视频帧率
     */
    @TableField(value = "`video_fps`")
    private Integer videoFps;

    /**
     * 同一时间检测到违法车辆播报次数映射
     */
    @TableField(value = "`announce_times`")
    private String announceTimes;

    /**
     * 最大连续播报同一音频次数
     */
    @TableField(value = "`max_announce_times`")
    private Integer maxAnnounceTimes;

    /**
     * 同一音频播报间隔（秒）
     */
    @TableField(value = "`announce_timeout`")
    private Integer announceTimeout;

    /**
     * 语音播报音量控制
     */
    @TableField(value = "`announce_volume`")
    private String announceVolume;

    /**
     * 开始播报时间
     */
    @TableField(value = "`unmute_start`")
    private Integer unmuteStart;

    /**
     * 结束播报时间
     */
    @TableField(value = "`unmute_end`")
    private Integer unmuteEnd;

    /**
     * 背向车辆收集车牌图像最大数量
     */
    @TableField(value = "`bike_plates_quantity`")
    private Integer bikePlatesQuantity;

    /**
     * 提交失败后重试间隔时间列表
     */
    @TableField(value = "`retry_intervals`", typeHandler = IntegerArrayJSONTypeHandler.class)
    private Integer[] retryIntervals;

    /**
     * 上报设备在线状态的间隔时间（秒）
     */
    @TableField(value = "`online_status_interval`")
    private Integer onlineStatusInterval;

    /**
     * 违法车辆检测是否以背面碰线优先
     */
    @TableField(value = "`backward_priority`")
    private Boolean backwardPriority;

    /**
     * 模型文件信息
     */
    @TableField(value = "`model_file`")
    private String modelFile;
    /**
     * 是否立即更新
     */
    @TableField(value = "`urgent`")
    private Boolean urgent;


}