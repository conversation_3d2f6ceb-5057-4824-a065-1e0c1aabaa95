package com.demo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class SiteAttendanceResponseDTO {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;
    private List<SiteDTO> sites;
    private StatisticsDTO statistics;

    @Data
    public static class SiteDTO {
        private String siteId;
        private LocationDTO location;
        private List<ShiftDTO> shifts;
        private List<StaffDTO> staff;
        private List<DeviceFaultRecordDTO> deviceFaultRecords;
        private String status;
    }

    @Data
    public static class LocationDTO {
        private String city;
        private String county;
        private String township;
        private String hamlet;
        private String site;
    }

    @Data
    public static class ShiftDTO {
        private String shiftName;
        private String startTime;
        private String endTime;
        private Integer staffCount;
    }

    @Data
    public static class StaffDTO {
        private Integer userId;
        private String userName;
        private String phone;
        private String deptName;
        private String status;
        private List<StaffShiftDTO> shifts;
    }

    @Data
    public static class StaffShiftDTO {
        private String shiftName;
        private String startTime;
        private String endTime;
        private String status;
        private List<AbnormalRecordDTO> abnormalRecords;
    }

    @Data
    public static class AbnormalRecordDTO {
        private String type;
        private String startTime;
        private String endTime;
        private String duration;
        private String description;
    }

    @Data
    public static class DeviceFaultRecordDTO {
        private String equipmentNumber;
        private String ip;
        private String startTime;
        private String endTime;
        private String duration;
        private String description;
    }

    @Data
    public static class StatisticsDTO {
        private Integer totalSites;
        private StatusCountDTO statusCount;
    }

    @Data
    public static class StatusCountDTO {
        private Integer onDuty;
        private Integer offDuty;
        private Integer rest;
        private Integer fault;
    }
} 