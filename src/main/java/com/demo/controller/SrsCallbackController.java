package com.demo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.entity.Device;
import com.demo.service.DeviceService;
import com.demo.service.StreamService;
import com.demo.service.VideoRecordingService;
import com.demo.service.VideoRecordingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api")
public class SrsCallbackController {
    @Autowired
    private StreamService streamService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private VideoRecordingService videoRecordingService;
    public static final Map<String, String> SRS_MAP = new HashMap<>();
    // 类级变量定义
    private static final Map<String, Boolean> lastPublishStatusMap = new HashMap<>();
    /**
     * 设备尝试推流时的回调 - 控制推流权限
     * SRS配置：on_publish http://your-backend/api/on_publish
     */
    @PostMapping("/on_publish")
    public ResponseEntity<?> onPublish(@RequestBody Map<String, Object> payload) {
        String streamName = (String) payload.get("stream");
        String app = (String) payload.get("app");
        String clientId = (String) payload.get("client_id");
        if (!SRS_MAP.containsKey(streamName)) {
            Device device = deviceService.getOne(
                    new LambdaQueryWrapper<Device>()
                            .like(Device::getStreamKey, streamName));
            // 使用 LambdaQueryWrapper 构建模糊查询条件
            SRS_MAP.put(streamName,device.getCounty()  + "-"+device.getTownship()+"-"+device.getHamlet()+"-"+device.getDeviceName());
        }
//        log.info("设备尝试推流，流名称: {}, 应用: {}, 客户端ID: {}", SRS_MAP.get(streamName), app, clientId);
        try {
            // 检查是否允许推流
            boolean isAllowed = streamService.isPublishAllowed(streamName);
            if (!isAllowed && (lastPublishStatusMap.getOrDefault(streamName, true) != isAllowed)) {
                log.info("拒绝流 {} 推流 - 当前无观看者或已超时", SRS_MAP.get(streamName));
            } else if (isAllowed && (lastPublishStatusMap.getOrDefault(streamName, false) != isAllowed)) {
                log.info("允许流 {} 推流", SRS_MAP.get(streamName));
            }
            // 更新状态记录
            lastPublishStatusMap.put(streamName, isAllowed);
            if (isAllowed) {
//                log.info("允许流 {} 推流", SRS_MAP.get(streamName));
                return ResponseEntity.ok().body(Map.of(
                    "code", 0,
                    "message", "允许推流"
                ));
            } else {
//                log.info("拒绝流 {} 推流 - 当前无观看者或已超时", SRS_MAP.get(streamName));
                return ResponseEntity.status(403).body(Map.of(
                    "code", 403,
                    "message", "当前无观看者，拒绝推流"
                ));
            }
        } catch (Exception e) {
            log.error("处理推流请求异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "服务器内部错误"
            ));
        }
    }

    /**
     * 设备停止推流时的回调 - 清理推流状态
     * SRS配置：on_unpublish http://your-backend/api/on_unpublish
     */
    @PostMapping("/on_unpublish")
    public ResponseEntity<?> onUnpublish(@RequestBody Map<String, Object> payload) {
        String streamName = (String) payload.get("stream");
        String app = (String) payload.get("app");
        String clientId = (String) payload.get("client_id");
        log.info("设备停止推流，流名称: {}, 应用: {}, 客户端ID: {}", SRS_MAP.get(streamName), app, clientId);
        try {
            // 清理推流相关状态
            streamService.onPublishStop(streamName);
            log.info("已清理流 {} 的推流状态", SRS_MAP.get(streamName));
            return ResponseEntity.ok().body(Map.of(
                "code", 0,
                "message", "推流状态已清理"
            ));
        } catch (Exception e) {
            log.error("处理停止推流请求异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "服务器内部错误"
            ));
        }
    }

    /**
     * 观看者开始播放时的回调 - 增加观看者计数并允许推流
     * SRS配置：on_play http://your-backend/api/on_play
     */
    @PostMapping("/on_play")
    public ResponseEntity<?> onPlay(@RequestBody Map<String, Object> payload) {
        String streamName = (String) payload.get("stream");
        String app = (String) payload.get("app");
        String clientId = (String) payload.get("client_id");
        String pageUrl = (String) payload.get("pageUrl");
        log.info("观看者开始播放，流名称: {}, 应用: {}, 客户端ID: {}, 页面: {}",
                SRS_MAP.get(streamName), app, clientId, pageUrl);
        try {
            // 增加观看者计数
            int viewerCount = streamService.addViewer(streamName);
            log.info("流 {} 当前观看者数量: {}", SRS_MAP.get(streamName), viewerCount);
            return ResponseEntity.ok().body(Map.of(
                "code", 0,
                "message", "允许播放",
                "viewerCount", viewerCount
            ));
        } catch (Exception e) {
            log.error("处理播放请求异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "服务器内部错误"
            ));
        }
    }

    /**
     * 观看者停止播放时的回调 - 减少观看者计数
     * SRS配置：on_stop http://your-backend/api/on_stop
     */
    @PostMapping("/on_stop")
    public ResponseEntity<?> onStop(@RequestBody Map<String, Object> payload) {
        String streamName = (String) payload.get("stream");
        String app = (String) payload.get("app");
        String clientId = (String) payload.get("client_id");
        log.info("观看者停止播放，流名称: {}, 应用: {}, 客户端ID: {}", streamName, app, clientId);
        try {
            // 减少观看者计数
            int viewerCount = streamService.removeViewer(streamName);
            log.info("流 {} 当前观看者数量: {}", streamName, viewerCount);
            
            return ResponseEntity.ok().body(Map.of(
                "code", 0,
                "message", "停止播放处理完成",
                "viewerCount", viewerCount
            ));
        } catch (Exception e) {
            log.error("处理停止播放请求异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "服务器内部错误"
            ));
        }
    }
    
    /**
     * 获取所有流的状态信息（监控接口）
     */
    @GetMapping("/stream/status")
    public ResponseEntity<?> getStreamStatus() {
        try {
            return ResponseEntity.ok().body(Map.of(
                "code", 0,
                "data", streamService.getAllStreamStatus(),
                "message", "获取流状态成功"
            ));
        } catch (Exception e) {
            log.error("获取流状态异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "获取流状态失败"
            ));
        }
    }
    
    /**
     * 手动控制流的推流权限（管理接口）
     */
    @PostMapping("/stream/{streamName}/permission")
    public ResponseEntity<?> setStreamPermission(
            @PathVariable String streamName,
            @RequestParam boolean allowed) {
        try {
            streamService.setPublishPermission(streamName, allowed);
            return ResponseEntity.ok().body(Map.of(
                "code", 0,
                "message", String.format("流 %s 的推流权限已设置为: %s", streamName, allowed)
            ));
        } catch (Exception e) {
            log.error("设置流权限异常: {}", e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "设置流权限失败"
            ));
        }
    }

    /**
     * SRS DVR录制回调 - 处理录制文件生成
     * SRS配置：on_dvr http://your-backend/api/on_dvr
     */
    @PostMapping("/on_dvr")
    public ResponseEntity<?> onDvr(@RequestBody Map<String, Object> payload) {
        String streamName = (String) payload.get("stream");
        String app = (String) payload.get("app");
        String file = (String) payload.get("file");
        String cwd = (String) payload.get("cwd");
        log.info("DVR录制回调，流名称: {}, 应用: {}, 文件: {}",
                SRS_MAP.get(streamName), app, file);
        try {
            // 调用录制服务处理DVR回调
            cn.dev33.satoken.util.SaResult result = videoRecordingService.handleDvrCallback(payload);

            if (result.getCode() == 200) {
                log.info("DVR文件处理成功: {}", file);
                return ResponseEntity.ok().body(Map.of(
                    "code", 0,
                    "message", "DVR回调处理成功"
                ));
            } else {
                log.error("DVR文件处理失败: {}", result.getMsg());
                return ResponseEntity.status(500).body(Map.of(
                    "code", 500,
                    "message", "DVR回调处理失败: " + result.getMsg()
                ));
            }
        } catch (Exception e) {
            log.error("处理DVR回调异常: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(Map.of(
                "code", 500,
                "message", "DVR回调处理异常"
            ));
        }
    }

}
