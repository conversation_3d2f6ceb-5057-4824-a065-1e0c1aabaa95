package com.demo.controller;

import cn.dev33.satoken.util.SaResult;
import com.demo.service.AccuratePersuasionService;
import com.demo.service.DeviceService;
import com.demo.service.IVillageInformationService;
import com.demo.service.IllegalRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Date;

/**
 * 大屏控制器
 * 用于处理大屏相关的请求
 */
@RestController
@RequestMapping("/largeScreen")
public class LargeScreenController {

    @Autowired
    IllegalRecordsService illegalRecordsService;
    @Autowired
    DeviceService deviceService;
    @Autowired
    AccuratePersuasionService accuratePersuasionService;
    @Autowired
    private IVillageInformationService villageInformationService;

    ///**
    // * 获取近七天的违法数量、劝导次数和处理率
    // * @return 包含统计数据的 SaResult 对象
    // */
    //@GetMapping("/weeklyStatistics")
    //public SaResult getWeeklyStatistics() {
    //    Map<String, Object> result = illegalRecordsService.getWeeklyStatisticsForECharts();
    //    return SaResult.data(result);
    //}

    /**
     * 获取按天统计的违法类型分布趋势
     */
    @GetMapping("/violationTrendsByDay")
    public SaResult violationTrendsByDay(@RequestParam("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
                                         @RequestParam(required = false) String city,
                                         @RequestParam(required = false) String county,
                                         @RequestParam(required = false) String township,
                                         @RequestParam(required = false) String hamlet,
                                         @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationTrendsByDay(date, city, county, township, hamlet, site);
    }

    /**
     * 获取按周统计的违法类型分布趋势
     */
    @GetMapping("/violationTrendsByWeek")
    public SaResult violationTrendsByWeek(@RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date startTime,
                                          @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date endTime,
                                          @RequestParam(required = false) String city,
                                          @RequestParam(required = false) String county,
                                          @RequestParam(required = false) String township,
                                          @RequestParam(required = false) String hamlet,
                                          @RequestParam(required = false) String site) {
        System.out.println(startTime);
        System.out.println(endTime);
        return illegalRecordsService.getViolationTrendsByWeek(startTime, endTime, city, county, township, hamlet, site);
    }

    /**
     * 获取按月统计的违法类型分布趋势
     */
    @GetMapping("/violationTrendsByMonth")
    public SaResult violationTrendsByMonth(int year, int month, @RequestParam(required = false) String city,
                                           @RequestParam(required = false) String county,
                                           @RequestParam(required = false) String township,
                                           @RequestParam(required = false) String hamlet,
                                           @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationTrendsByMonth(year, month, city, county, township, hamlet, site);
    }

    /**
     * 获取按年统计的违法类型分布趋势
     */
    @GetMapping("/violationTrendsByYear")
    public SaResult violationTrendsByYear(int year, @RequestParam(required = false) String city,
                                          @RequestParam(required = false) String county,
                                          @RequestParam(required = false) String township,
                                          @RequestParam(required = false) String hamlet,
                                          @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationTrendsByYear(year, city, county, township, hamlet, site);
    }


    /**
     * 获取违法概览(当天)
     */
    @GetMapping("/DayStatistics")
    public SaResult getDayStatistics(@RequestParam(required = false) String city,
                                     @RequestParam(required = false) String county,
                                     @RequestParam(required = false) String township,
                                     @RequestParam(required = false) String hamlet,
                                     @RequestParam(required = false) String site,
                                     @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        return illegalRecordsService.DayStatistics(city, county, township, hamlet, site, date);
    }

    /**
     * 获取违法概览(当月)
     */
    @GetMapping("/MonthStatistics")
    public SaResult getMonthStatistics(@RequestParam(required = false) String city,
                                       @RequestParam(required = false) String county,
                                       @RequestParam(required = false) String township,
                                       @RequestParam(required = false) String hamlet,
                                       @RequestParam(required = false) String site,
                                       @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return illegalRecordsService.MonthStatistics(city, county, township, hamlet, site, date);
    }

    /**
     * 设备概览
     */
    @GetMapping("/deviceOverview")
    public SaResult getDeviceOverview(@RequestParam(required = false) String city,
                                      @RequestParam(required = false) String county,
                                      @RequestParam(required = false) String township,
                                      @RequestParam(required = false) String hamlet,
                                      @RequestParam(required = false) String site) {
        return deviceService.getDeviceOverview(city, county, township, hamlet, site);
    }

    ///**
    // * 获取今日，本周，本月数据
    // */
    //@GetMapping("/illegalData")
    //public SaResult getIllegalData() {
    //    return illegalRecordsService.getIllegalData();
    //}

    /**
     * 违法类型分析(今天)
     */
    @GetMapping("/violationTypeAnalysis")
    public SaResult getViolationTypeAnalysis(@RequestParam(required = false) String city,
                                             @RequestParam(required = false) String county,
                                             @RequestParam(required = false) String township,
                                             @RequestParam(required = false) String hamlet,
                                             @RequestParam(required = false) String site) {
        return illegalRecordsService.getViolationTypeAnalysis(city, county, township, hamlet, site);
    }

    /**
     * 违法类型月度分析
     */
    @GetMapping("/violationTypeAnalysisByMonth")
    public SaResult getViolationTypeAnalysisByMonth(@RequestParam(required = false) String city,
                                                    @RequestParam(required = false) String county,
                                                    @RequestParam(required = false) String township,
                                                    @RequestParam(required = false) String hamlet,
                                                    @RequestParam(required = false) String site,
                                                    @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return illegalRecordsService.getViolationTypeAnalysisByMonth(city, county, township, hamlet, site,date);
    }

//    /**
//     * 违法类型周分析
//     */
//    @GetMapping("/violationTypeAnalysisByWeek")
//    public SaResult getViolationTypeAnalysisByWeek(@RequestParam(required = false) String city,
//                                                   @RequestParam(required = false) String county,
//                                                   @RequestParam(required = false) String township,
//                                                   @RequestParam(required = false) String hamlet,
//                                                   @RequestParam(required = false) String site) {
//        return illegalRecordsService.getViolationTypeAnalysisByWeek(city, county, township, hamlet, site);
//    }

    /**
     * 违法类型年度分析
     */
    @GetMapping("/violationTypeAnalysisByYear")
    public SaResult getViolationTypeAnalysisByYear(@RequestParam(required = false) String city,
                                                   @RequestParam(required = false) String county,
                                                   @RequestParam(required = false) String township,
                                                   @RequestParam(required = false) String hamlet,
                                                   @RequestParam(required = false) String site,
                                                   @RequestParam(required = false) String year) {
        return illegalRecordsService.getViolationTypeAnalysisByYear(city, county, township, hamlet, site,year);
    }

    /**
     * 高发地段TOP10(今天)
     */
    @GetMapping("/topLocationsByDay")
    public SaResult topLocationsByDay(@RequestParam(required = false) String city,
                                      @RequestParam(required = false) String county,
                                      @RequestParam(required = false) String township,
                                      @RequestParam(required = false) String hamlet,
                                      @RequestParam(required = false) String site,
                                      @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {

        return illegalRecordsService.topLocationsByDay(city, county, township, hamlet, site, date);
    }

    /**
     * 高发地段TOP10(当月)
     */
    @GetMapping("/topLocationsByMonth")
    public SaResult topLocationsByMonth(@RequestParam(required = false) String city,
                                        @RequestParam(required = false) String county,
                                        @RequestParam(required = false) String township,
                                        @RequestParam(required = false) String hamlet,
                                        @RequestParam(required = false) String site,
                                        @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return illegalRecordsService.topLocationsByMonth(city, county, township, hamlet, site, date);
    }

    /**
     * 获取点位详细数据(天)
     */
    @GetMapping("/getPointDetailDataByDay")
    public SaResult getPointDetailDataByDay(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) int year,
            @RequestParam(required = false) int month ,
            @RequestParam(required = false) int day) {
        return illegalRecordsService.getPointDetailDataByDay(city, county, township, hamlet, site,year,month,day);
    }

    /**
     * 获取点位详细数据(月)
     */
    @GetMapping("/getPointDetailDataByMonth")
    public SaResult getPointDetailDataByMonth(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) int year,
            @RequestParam(required = false) int month) {
        return illegalRecordsService.getPointDetailDataByMonth(city, county, township, hamlet, site,year,month);
    }
    /**
     * 获取点位详细数据(年)
     */
    @GetMapping("/getPointDetailDataByYear")
    public SaResult getPointDetailDataByYear(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) int year) {
        return illegalRecordsService.getPointDetailDataByYear(city, county, township, hamlet, site,year);
    }

    /**
     * 精准劝导处置统计（年）
     * DisposalStatistics
     */
    @GetMapping("/getDisposalStatistics")
    public SaResult getDisposalStatistics(@RequestParam(required = false) String city,
                                          @RequestParam(required = false) String county,
                                          @RequestParam(required = false) String township,
                                          @RequestParam(required = false) String hamlet,
                                          @RequestParam(required = false) String site,
                                          @RequestParam(required = false) String year ) {
        return accuratePersuasionService.getDisposalStatistics(city, county, township, hamlet, site,year);
    }

    /**
     * 精准劝导处置统计（月）
     * DisposalStatistics
     */
    @GetMapping("/getDisposalStatisticsByMonth")
    public SaResult getDisposalStatisticsByMonth(@RequestParam(required = false) String city,
                                                 @RequestParam(required = false) String county,
                                                 @RequestParam(required = false) String township,
                                                 @RequestParam(required = false) String hamlet,
                                                 @RequestParam(required = false) String site,
                                                 @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                                 @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return accuratePersuasionService.getDisposalStatisticsByMonth(city, county, township, hamlet, site,startTime,endTime);
    }

    /**
     * 获取精准劝导处置效率分析（按地点返回）（日）
     */

    @GetMapping("/getDisposalEfficiencyAnalysisByDay")
    public SaResult getDisposalEfficiencyAnalysisByDay(@RequestParam(required = false) String city,
                                                       @RequestParam(required = false) String county,
                                                       @RequestParam(required = false) String township,
                                                       @RequestParam(required = false) String hamlet,
                                                       @RequestParam(required = false) String site,
                                                       @RequestParam(required = false) int year,
                                                       @RequestParam(required = false) int month,
                                                       @RequestParam(required = false) int day) {
        return accuratePersuasionService.getDisposalEfficiencyAnalysisByDay(city, county, township, hamlet, site, year, month, day);
    }

    /**
     * 获取精准劝导处置效率分析（按地点返回）（月）
     */
    @GetMapping("/getDisposalEfficiencyAnalysisByMonth")
    public SaResult getDisposalEfficiencyAnalysisByMonth(@RequestParam(required = false) String city,
                                                         @RequestParam(required = false) String county,
                                                         @RequestParam(required = false) String township,
                                                         @RequestParam(required = false) String hamlet,
                                                         @RequestParam(required = false) String site,
                                                         @RequestParam(required = false) int year,
                                                         @RequestParam(required = false) int month) {
        return accuratePersuasionService.getDisposalEfficiencyAnalysisByMonth(city, county, township, hamlet, site, year, month);
    }

    /**
     * 获取精准劝导处置效率分析（按地点返回）（年）
     */
    @GetMapping("/getDisposalEfficiencyAnalysisByYear")
    public SaResult getDisposalEfficiencyAnalysisByYear(@RequestParam(required = false) String city,
                                                        @RequestParam(required = false) String county,
                                                        @RequestParam(required = false) String township,
                                                        @RequestParam(required = false) String hamlet,
                                                        @RequestParam(required = false) String site,
                                                        @RequestParam(required = false) int year) {
        return accuratePersuasionService.getDisposalEfficiencyAnalysisByYear(city, county, township, hamlet, site,year);
    }
    /**
     * 获取当天违法类型统计
     */
    @GetMapping("/getViolationTypeStatistics")
    public SaResult getViolationTypeStatistics(@RequestParam(required = false) String city,
                                               @RequestParam(required = false) String county,
                                               @RequestParam(required = false) String township,
                                               @RequestParam(required = false) String hamlet,
                                               @RequestParam(required = false) String site,
                                               @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        return illegalRecordsService.getViolationTypeStatistics(city, county, township, hamlet, site,date);
    }

    /**
     * 违法劝导率统计
     */
    @GetMapping("/getPersuasionStats")
    public SaResult getPersuasionStats(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return illegalRecordsService.getPersuasionStatsByWorkTime(
                city, county, township, hamlet, site, startDate, endDate);
    }

    /**
     * 获取点位违法趋势统计(按天)
     */
    @GetMapping("/getLocationTrends")
    public SaResult getLocationTrends(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return illegalRecordsService.getLocationTrends(
                city, county, township, hamlet, site, startDate, endDate);
    }
     /**
      * 获取点位违法趋势统计(按年统计)
      */
     @GetMapping("/getLocationTrendsByYear")
     public SaResult getLocationTrendsByYear(
             @RequestParam(required = false) String city,
             @RequestParam(required = false) String county,
             @RequestParam(required = false) String township,
             @RequestParam(required = false) String hamlet,
             @RequestParam(required = false) String site,
             @RequestParam int year) {
         return illegalRecordsService.getLocationTrendsByYear(
                 year,city, county, township, hamlet, site);
     }

    /**
     * 获取当天出勤情况
     */
    @GetMapping("/getAttendance")
    public SaResult getAttendance(@RequestParam(required = false) String city,
                                  @RequestParam(required = false) String county,
                                  @RequestParam(required = false) String township,
                                  @RequestParam(required = false) String hamlet,
                                  @RequestParam(required = false) String site,
                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return illegalRecordsService.getAttendance(city, county, township, hamlet, site, date);
    }

    /**
     * 获取当天出勤勤务安排，在岗时长，在岗率（日）
     */
    @GetMapping("/getAttendanceAndWorkTimeByday")
    public SaResult getAttendanceAndWorkTimeByday(@RequestParam(required = false) String city,
                                                  @RequestParam(required = false) String county,
                                                  @RequestParam(required = false) String township,
                                                  @RequestParam(required = false) String hamlet,
                                                  @RequestParam(required = false) String site,
                                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return illegalRecordsService.getAttendanceAndWorkTimeByday(city, county, township, hamlet, site, date);
    }

    /**
     * 获取当天出勤勤务安排，在岗时长，在岗率（月）
     */
    @GetMapping("/getAttendanceAndWorkTimeByMonth")
    public SaResult getAttendanceAndWorkTimeByMonth(@RequestParam(required = false) String city,
                                                    @RequestParam(required = false) String county,
                                                    @RequestParam(required = false) String township,
                                                    @RequestParam(required = false) String hamlet,
                                                    @RequestParam(required = false) String site,
                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return illegalRecordsService.getAttendanceAndWorkTimeByMonth(city, county, township, hamlet, site, date);
    }

    /**
     * 获取当天出勤勤务安排，在岗时长，在岗率（年）
     */
    @GetMapping("/getAttendanceAndWorkTimeByYears")
    public SaResult getAttendanceAndWorkTimeByYears(@RequestParam(required = false) String city,
                                                    @RequestParam(required = false) String county,
                                                    @RequestParam(required = false) String township,
                                                    @RequestParam(required = false) String hamlet,
                                                    @RequestParam(required = false) String site,
                                                    @RequestParam @DateTimeFormat(pattern = "yyyy") Date date) {
        return illegalRecordsService.getAttendanceAndWorkTimeByYears(city, county, township, hamlet, site, date);
    }

    /**
     * 获取地图点位详情数据
     */
    @GetMapping("/getMapPointDetail")
    public SaResult getMapPointDetail(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site) {
        return illegalRecordsService.getMapPointDetail(city, county, township, hamlet, site);
    }

    /**
     * 获取24小时违法趋势数据
     */
    @GetMapping("/getHourlyViolationStats")
    public SaResult getHourlyViolationStats(
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String county,
            @RequestParam(required = false) String township,
            @RequestParam(required = false) String hamlet,
            @RequestParam(required = false) String site,
            @RequestParam(value = "startTime", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date startTime,
            @RequestParam(value = "endTime", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date endTime) {
        return illegalRecordsService.getHourlyViolationStats(city, county, township, hamlet, site, startTime,endTime);
    }

    /**
     * 地图点位获取违法类型统计(按照时间)
     */
    @GetMapping("/getViolationTypeStatisticsByMap")
    public SaResult getViolationTypeStatisticsByMap(@RequestParam(required = false) String city,
                                                    @RequestParam(required = false) String county,
                                                    @RequestParam(required = false) String township,
                                                    @RequestParam(required = false) String hamlet,
                                                    @RequestParam(required = false) String site,
                                                    @RequestParam(value = "startTime", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date startTime,
                                                    @RequestParam(value = "endTime", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss") Date endTime) {
        return illegalRecordsService.getViolationTypeStatisticsByMap(city, county, township, hamlet, site, startTime, endTime);

    }

    /**
     * 获取基础数据(按照点位)
     */
    @GetMapping("/getBasicDataByMap")
    public SaResult getBasicDataByMap(@RequestParam(required = false) String city,
                                     @RequestParam(required = false) String county,
                                     @RequestParam(required = false) String township,
                                     @RequestParam(required = false) String hamlet,
                                     @RequestParam(required = false) String site) {
        return villageInformationService.getBasicDataByMap(city, county, township, hamlet, site);
    }

}
